import { Inject, Injectable } from '@nestjs/common'
import { CrudableBase<PERSON>CHandler, IPCHandlerError } from '@/infra/types/CrudableBaseIPCHandler.js'
import { UploadTaskService } from './upload-task.service.js'
import { UploadTask } from '@app/shared/types/database.types.js'

/**
 * 上传任务 IPC 处理器
 */
@Injectable()
export class UploadTaskIPCHandler extends CrudableBaseIPCHandler<'uploadTask'> {

  constructor(
    @Inject(UploadTaskService) readonly uploadTaskService: UploadTaskService
  ) {
    super(uploadTaskService, 'uploadTask')
  }

  /**
   * 注册额外的 IPC 处理程序
   */
  protected registerExtraHandlers(): void {
    // 获取用户上传任务
    this.registerHandler('getUserTasks', data => {
      return this.uploadTaskService.getUserTasks(data)
    }
    )

    // 获取文件夹下的上传任务
    this.registerHandler('getTasksByFolder',
      data => {
        return this.uploadTaskService.getTasksByFolder(data)
      } )

    // 搜索上传任务
    this.registerHandler(
      'searchTasks',
      data => {
        return this.uploadTaskService.searchTasks(data)
      }
    )

    // 获取上传任务统计
    this.registerHandler('getTaskStats', data => {
      return this.uploadTaskService.getTaskStats(data)
    }
    )

    // 开始上传任务
    this.registerHandler('startUpload', data => {
      return this.uploadTaskService.startUpload(data)
    })

    // 暂停上传任务
    this.registerHandler('pauseUpload', data => {
      return  this.uploadTaskService.pauseUpload(data)
    })

    // 恢复上传任务
    this.registerHandler('resumeUpload', data => {
      return  this.uploadTaskService.resumeUpload(data)
    })

    // 取消上传任务
    this.registerHandler('cancelUpload', data => {
      return this.uploadTaskService.cancelUpload(data)
    })

    // 重试上传任务
    this.registerHandler('retryUpload', data => {
      return this.uploadTaskService.retryUpload(data)
    }
    )

    // 批量操作上传任务
    this.registerHandler('batchOperation', data => {
      return this.uploadTaskService.batchOperation(data)
    }
    )

    // 获取上传队列配置
    this.registerHandler('getQueueConfig',  () => {
      return this.uploadTaskService.getQueueConfig()
    })

    // 更新上传队列配置
    this.registerHandler(
      'updateQueueConfig', config => {
        return this.uploadTaskService.updateQueueConfig(config)
      })

    // 获取当前上传队列状态
    this.registerHandler('getQueueStatus', () => {
      return this.uploadTaskService.getQueueStatus()
    })

    // 清理已完成的任务
    this.registerHandler('cleanupCompleted', data => {
      return this.uploadTaskService.cleanupCompleted(data)
    })

    // 选择文件
    this.registerHandler( 'selectFiles', data => this.uploadTaskService.selectFiles(data))

    // 从本地路径上传文件
    this.registerHandler('uploadFromPath', data => {
      return this.uploadTaskService.uploadFromPath(data)
    })

    // 上传文件夹
    this.registerHandler('uploadFolder', data => {
      return this.uploadTaskService.uploadFolder(data)
    })
  }
}
