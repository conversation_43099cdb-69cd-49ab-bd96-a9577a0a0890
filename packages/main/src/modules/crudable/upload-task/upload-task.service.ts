import { forwardRef, Inject, Injectable, Logger } from '@nestjs/common'
import { dialog } from 'electron'
import { statSync, readdirSync } from 'fs'
import { basename, join, extname, relative } from 'path'
import { UploadTaskModel } from '@/infra/models/UploadTaskModel.js'
import { UploadTaskRepository } from './upload-task.repository.js'
import { CrudableBaseService } from '@/infra/types/CrudableBaseService.js'

import { UploadTask } from '@app/shared/types/database.types.js'
import { UploadQueueManager } from './upload-queue-manager.js'
import { UploadTaskIPCClientUniqueUtilities } from '@app/shared/types/ipc/upload-task.js'
import { IPCHandlerError } from '@/infra/types/CrudableBaseIPCHandler.js'
import { createFileFilters } from '@app/shared/file.js'
import { RequestService } from '@/modules/global/request.service.js'

/**
 * 文件夹结构节点
 */
interface FolderNode {
  name: string
  path: string
  relativePath: string
  folderUuid?: string
  children: FolderNode[]
  files: FileNode[]
}

/**
 * 文件节点
 */
interface FileNode {
  name: string
  path: string
  relativePath: string
  size: number
  type: string
}

/**
 * 文件夹上传参数
 */
interface FolderUploadParams {
  folderPath: string
  parentFolderUuid: string
  resourceType: 'media' | 'paster' | 'music' | 'voice'
  uid: string
  teamId?: number
  fileTypes?: string[]
  maxSize?: number
}

/**
 * 文件夹上传结果
 */
interface FolderUploadResult {
  success: boolean
  batchId?: string
  taskIds?: number[]
  totalFiles?: number
  error?: string
}

/**
 * 上传任务服务类
 */
@Injectable()
export class UploadTaskService extends CrudableBaseService<
  UploadTaskModel,
  UploadTask.CreateParams,
  UploadTask.UpdateParams,
  UploadTask.QueryParams,
  UploadTaskRepository
>  implements UploadTaskIPCClientUniqueUtilities {

  private readonly logger = new Logger('UploadTaskService')

  /**
   * 默认队列配置
   */
  private queueConfig: UploadTask.QueueConfig = {
    max_concurrent_uploads: 3,
    retry_attempts: 3,
    retry_delay: 5000,
    chunk_size: 1024 * 1024 // 1MB
  }

  constructor(
    @Inject(UploadTaskRepository)  repository: UploadTaskRepository,
    @Inject(forwardRef(() => UploadQueueManager)) private queueManager: UploadQueueManager,
    @Inject(RequestService) private readonly requestService: RequestService
  ) {
    super(repository)
    this.scheduleInitialization()
  }
  startUpload(data: { id: number }): boolean {
    if (!data) {
      throw new IPCHandlerError('开始上传任务参数不能为空')
    }
    if (data.id === undefined || data.id === null) {
      throw new IPCHandlerError('任务ID不能为空')
    }
    return this.updateTaskStatus(data.id, UploadTask.Status.UPLOADING)
  }

  async pauseUpload(data: { id: number }): Promise<boolean> {
    if (!data) {
      throw new IPCHandlerError('暂停上传任务参数不能为空')
    }
    if (data.id === undefined || data.id === null) {
      throw new IPCHandlerError('任务ID不能为空')
    }

    const { id } = data
    const task = this.repository.findById(id)
    if (!task) {
      throw new Error('任务不存在')
    }

    if (!task.canPause()) {
      throw new Error('任务当前状态不允许暂停')
    }

    // 调用队列管理器的暂停方法，它会处理正在上传的任务或直接更新状态
    if (this.queueManager) {
      this.logger.debug(` 调用队列管理器暂停任务 ${id}`)
      return await this.queueManager.pauseUpload(id)
    }

    // 如果没有队列管理器，直接更新数据库状态
    return this.updateTaskStatus(id, UploadTask.Status.PAUSED)
  }
  async resumeUpload(data: { id: number }): Promise<boolean> {
    if (!data) {
      throw new IPCHandlerError('恢复上传任务参数不能为空')
    }
    if (data.id === undefined || data.id === null) {
      throw new IPCHandlerError('任务ID不能为空')
    }

    const { id } = data
    const task = this.repository.findById(id)
    if (!task) {
      throw new Error('任务不存在')
    }

    if (!task.canResume()) {
      throw new Error('任务当前状态不允许恢复')
    }

    // 调用队列管理器的恢复方法，它会处理暂停的任务或直接更新状态
    if (this.queueManager) {
      this.logger.debug(` 调用队列管理器恢复任务 ${id}`)
      return await this.queueManager.resumeUpload(id)
    }

    // 如果没有队列管理器，直接更新状态为等待
    return this.updateTaskStatus(id, UploadTask.Status.PENDING)
  }
  cancelUpload(data: { id: number }): boolean  {
    if (!data) {
      throw new IPCHandlerError('取消上传任务参数不能为空')
    }
    if (data.id === undefined || data.id === null) {
      throw new IPCHandlerError('任务ID不能为空')
    }

    const { id } = data
    const task = this.repository.findById(id)
    if (!task) {
      throw new Error('任务不存在')
    }

    if (!task.canCancel()) {
      throw new Error('任务当前状态不允许取消')
    }

    return this.updateTaskStatus(id, UploadTask.Status.CANCELLED, '用户取消')
  }

  retryUpload(data: { id: number }): boolean {
    if (!data) {
      throw new IPCHandlerError('重试上传任务参数不能为空')
    }
    if (data.id === undefined || data.id === null) {
      throw new IPCHandlerError('任务ID不能为空')
    }

    const { id } = data
    const task = this.repository.findById(id)
    if (!task) {
      throw new Error('任务不存在')
    }

    if (!task.canRetry()) {
      throw new Error('任务当前状态不允许重试')
    }

    task.reset()
    return this.repository.update(id, {
      progress: task.progress,
      status: task.status,
      reason: task.reason
    })
  }
  batchOperation(data: UploadTask.BatchParams): number {
    if (!data) {
      throw new IPCHandlerError('批量操作参数不能为空')
    }
    if (!data.ids || !Array.isArray(data.ids) || data.ids.length === 0) {
      throw new IPCHandlerError('任务ID数组不能为空')
    }
    if (!data.action) {
      throw new IPCHandlerError('操作类型不能为空')
    }

    switch (data.action) {
      case 'pause': {
        return this.batchUpdateStatus(data.ids, UploadTask.Status.PAUSED)
      }
      case 'resume': {
        return this.batchUpdateStatus(data.ids, UploadTask.Status.PENDING)
      }
      case 'cancel': {
        return this.batchUpdateStatus(data.ids, UploadTask.Status.CANCELLED, '用户取消')
      }
      case 'retry': {
        let retryCount = 0
        for (const id of data.ids) {
          try {
            if (this.retryUpload({ id })) {
              retryCount++
            }
          } catch (error) {
            // 忽略单个任务重试失败
          }
        }
        return retryCount
      }
      case 'delete': {
        return this.batchDelete(data.ids)
      }
      default: {
        throw new IPCHandlerError(`不支持的操作类型: ${data.action}`)
      }
    }
  }

  /**
   * 设置队列管理器引用（由队列管理器调用）
   */
  setQueueManager(queueManager: UploadQueueManager): void {
    this.queueManager = queueManager
  }

  /**
   * 调度初始化，等待数据库准备就绪
   */
  private scheduleInitialization(): void {
    const checkAndInit = () => {
      try {
        // 尝试访问数据库，如果失败则继续等待
        this.repository.resetUploadingTasks()
        this.logger.debug(' 初始化成功')
      } catch (error) {
        // 数据库还未准备好，继续等待
        setTimeout(checkAndInit, 2000)
      }
    }

    // 延迟开始检查
    setTimeout(checkAndInit, 3000)
  }

  /**
   * 获取用户上传任务
   */
  getUserTasks(data: { uid: string, status?: UploadTask.Status, teamId?: number | null }): UploadTaskModel[] {
    if (!data) {
      throw new IPCHandlerError('获取用户上传任务参数不能为空')
    }
    if (!data.uid) {
      throw new IPCHandlerError('用户ID不能为空')
    }

    try {
      const { uid, status, teamId } = data
      return this.repository.findUserTasks(uid, status, teamId)
    } catch (error: any) {
      throw new Error(`获取用户上传任务失败: ${error.message}`)
    }
  }

  /**
   * 获取文件夹下的上传任务
   */
  getTasksByFolder(data: { folderId: string, uid: string, teamId?: number | null }): UploadTaskModel[] {
    if (!data) {
      throw new IPCHandlerError('获取文件夹上传任务参数不能为空')
    }
    if (!data.folderId) {
      throw new IPCHandlerError('文件夹ID不能为空')
    }
    if (!data.uid) {
      throw new IPCHandlerError('用户ID不能为空')
    }
    try {
      const { folderId, uid, teamId } = data
      return this.repository.findTasksByFolder(folderId, uid, teamId)
    } catch (error: any) {
      throw new Error(`获取文件夹上传任务失败: ${error.message}`)
    }
  }

  /**
   * 根据状态获取任务
   */
  getTasksByStatus(status: UploadTask.Status, uid?: string, teamId?: number | null): UploadTaskModel[] {
    try {
      return this.repository.findTasksByStatus(status, uid, teamId)
    } catch (error: any) {
      throw new Error(`根据状态获取上传任务失败: ${error.message}`)
    }
  }

  /**
   * 搜索上传任务
   */
  searchTasks(data: { keyword: string, uid: string, teamId?: number | null }): UploadTaskModel[] {
    if (!data) {
      throw new IPCHandlerError('搜索上传任务参数不能为空')
    }
    if (!data.keyword) {
      throw new IPCHandlerError('搜索关键词不能为空')
    }
    if (!data.uid) {
      throw new IPCHandlerError('用户ID不能为空')
    }

    try {
      const { keyword, uid, teamId } = data
      return this.repository.search(keyword, uid, teamId)
    } catch (error: any) {
      throw new Error(`搜索上传任务失败: ${error.message}`)
    }
  }

  /**
   * 获取任务统计
   */
  getTaskStats(data: { uid: string, teamId?: number | null }): UploadTask.StatsResult {
    if (!data) {
      throw new IPCHandlerError('获取上传任务统计参数不能为空')
    }
    if (!data.uid) {
      throw new IPCHandlerError('用户ID不能为空')
    }

    try {
      const { uid, teamId } = data
      return this.repository.getTaskStats(uid, teamId)
    } catch (error: any) {
      throw new Error(`获取上传任务统计失败: ${error.message}`)
    }
  }

  /**
   * 更新任务进度
   */
  updateTaskProgress(id: number, progress: number): boolean {
    try {
      const task = this.repository.findById(id)
      if (!task) {
        throw new Error('任务不存在')
      }

      task.updateProgress(progress)
      return this.repository.update(id, {
        progress: task.progress
      })
    } catch (error: any) {
      throw new Error(`更新任务进度失败: ${error.message}`)
    }
  }

  /**
   * 更新任务状态
   */
  updateTaskStatus(id: number, status: UploadTask.Status, reason?: string): boolean {
    try {
      const task = this.repository.findById(id)
      if (!task) {
        throw new Error('任务不存在')
      }

      task.setStatus(status, reason)
      return this.repository.update(id, {
        status: task.status,
        reason: task.reason
      })
    } catch (error: any) {
      throw new Error(`更新任务状态失败: ${error.message}`)
    }
  }

  /**
   * 批量更新任务状态
   */
  batchUpdateStatus(ids: number[], status: UploadTask.Status, reason?: string): number {
    try {
      return this.repository.batchUpdateStatus(ids, status, reason)
    } catch (error: any) {
      throw new Error(`批量更新任务状态失败: ${error.message}`)
    }
  }

  /**
   * 获取队列配置
   */
  getQueueConfig(): UploadTask.QueueConfig {
    return { ...this.queueConfig }
  }

  /**
   * 更新队列配置
   */
  updateQueueConfig(config: Partial<UploadTask.QueueConfig>): boolean {
    if (!config) {
      throw new IPCHandlerError('队列配置不能为空')
    }
    try {
      this.queueConfig = { ...this.queueConfig, ...config }
      return true
    } catch (error: any) {
      throw new Error(`更新队列配置失败: ${error.message}`)
    }
  }

  /**
   * 获取队列状态
   */
  getQueueStatus(): {
    active_count: number
    pending_count: number
    paused_count: number
    max_concurrent: number
  } {
    try {
      const uploadingTasks = this.repository.findTasksByStatus(UploadTask.Status.UPLOADING)
      const pendingTasks = this.repository.findTasksByStatus(UploadTask.Status.PENDING)
      const pausedTasks = this.repository.findTasksByStatus(UploadTask.Status.PAUSED)

      return {
        active_count: uploadingTasks.length,
        pending_count: pendingTasks.length,
        paused_count: pausedTasks.length,
        max_concurrent: this.queueConfig.max_concurrent_uploads
      }
    } catch (error: any) {
      throw new Error(`获取队列状态失败: ${error.message}`)
    }
  }

  /**
   * 清理已完成的任务
   */
  cleanupCompleted(data: { uid: string, teamId?: number | null }): number {
    if (!data) {
      throw new IPCHandlerError('清理已完成任务参数不能为空')
    }
    if (!data.uid) {
      throw new IPCHandlerError('用户ID不能为空')
    }

    try {
      const { uid, teamId } = data
      return this.repository.cleanupCompleted(uid, teamId)
    } catch (error: any) {
      throw new Error(`清理已完成任务失败: ${error.message}`)
    }
  }

  /**
   * 选择文件
   */
  async selectFiles(
    data: {
      multiple?: boolean,
      filters?: Array<{ name: string, extensions: string[] }>,
      folder?: boolean
    }
  ): Promise<string[]> {
    try {
      const { multiple = false, filters, folder = false } = data
      const defaultFilters = createFileFilters(['VIDEO', 'AUDIO', 'IMAGE'])
      const properties: Electron.OpenDialogOptions['properties'] = []

      if (folder) {
        properties.push('openDirectory')
      } else {
        properties.push('openFile')
      }

      if (multiple) {
        properties.push('multiSelections')
      }

      const result = await dialog.showOpenDialog({
        properties,
        filters: folder ? undefined : (filters || defaultFilters)
      })

      return result.canceled ? [] : result.filePaths
    } catch (error: any) {
      this.logger.error(' 选择文件失败:', error)
      return []
    }
  }

  /**
   * 从本地路径上传文件
   */
  async uploadFromPath(
    data: {
      taskId: number,
      filePath: string
    }
  ): Promise<{ success: boolean, url?: string, error?: string }> {
    if (!data) {
      throw new IPCHandlerError('从路径上传文件参数不能为空')
    }
    if (!data.taskId) {
      throw new IPCHandlerError('任务ID不能为空')
    }
    if (!data.filePath) {
      throw new IPCHandlerError('文件路径不能为空')
    }

    try {
      const { taskId, filePath } = data
      // 获取任务信息
      const task = this.repository.findById(taskId)
      if (!task) {
        return { success: false, error: '任务不存在' }
      }

      // 检查任务状态
      if (!task.isPending() && !task.isPaused() && !task.isFailed()) {
        return { success: false, error: '任务当前状态不允许上传' }
      }

      // 检查文件是否存在
      try {
        const stats = statSync(filePath)

        if (!stats.isFile()) {
          return { success: false, error: '指定路径不是文件' }
        }
      } catch {
        return { success: false, error: '文件不存在或无法访问' }
      }

      const fileInfo = await this.queueManager.calculateFileHash(filePath)

      this.logger.debug(` 文件信息: ${JSON.stringify(fileInfo)}`)

      this.repository.update(taskId, {
        local_path: filePath,
        name: basename(filePath),
        hash: fileInfo.md5Content,
        size: fileInfo.size,
        status: UploadTask.Status.PENDING,
      })

      this.logger.debug(` 任务 ${taskId} 已添加到上传队列，文件路径: ${filePath}`)
      return { success: true }
    } catch (error: any) {
      this.logger.error(' 从路径上传文件失败:', error)
      this.updateTaskStatus(data.taskId, UploadTask.Status.FAILED, error.message || '上传异常')
      return { success: false, error: error.message || '上传异常' }
    }
  }

  /**
   * 递归遍历文件夹结构
   */
  private async traverseFolder(
    folderPath: string,
    basePath: string,
    fileTypes: string[] = ['video/*', 'image/*', 'audio/mpeg'],
    maxSize: number = 100 * 1024 * 1024
  ): Promise<FolderNode> {
    const stats = statSync(folderPath)

    if (!stats.isDirectory()) {
      throw new Error('指定路径不是文件夹')
    }

    const folderName = basename(folderPath)
    const relativePath = relative(basePath, folderPath)

    const folderNode: FolderNode = {
      name: folderName,
      path: folderPath,
      relativePath: relativePath || folderName,
      children: [],
      files: []
    }

    try {
      const items = readdirSync(folderPath)

      for (const item of items) {
        const itemPath = join(folderPath, item)
        const itemStats = statSync(itemPath)

        if (itemStats.isDirectory()) {
          // 递归处理子文件夹
          const childFolder = await this.traverseFolder(itemPath, basePath, fileTypes, maxSize)
          folderNode.children.push(childFolder)
        } else if (itemStats.isFile()) {
          // 处理文件
          const fileNode = this.createFileNode(itemPath, basePath, itemStats, fileTypes, maxSize)
          if (fileNode) {
            folderNode.files.push(fileNode)
          }
        }
      }
    } catch (error) {
      this.logger.error(`遍历文件夹失败: ${folderPath}`, error)
      throw new Error(`无法读取文件夹: ${folderPath}`)
    }

    return folderNode
  }

  /**
   * 创建文件节点
   */
  private createFileNode(
    filePath: string,
    basePath: string,
    stats: any,
    fileTypes: string[],
    maxSize: number
  ): FileNode | null {
    const fileName = basename(filePath)
    const relativePath = relative(basePath, filePath)
    const fileExt = extname(fileName).toLowerCase()

    // 获取文件的 MIME 类型
    const mimeType = this.getMimeType(fileExt)

    // 检查文件类型
    const isValidType = fileTypes.some(type => {
      if (type.endsWith('/*')) {
        const category = type.split('/')[0]
        return mimeType.startsWith(category + '/')
      }
      return mimeType === type
    })

    // 检查文件大小
    if (!isValidType || stats.size > maxSize) {
      return null
    }

    return {
      name: fileName,
      path: filePath,
      relativePath,
      size: stats.size,
      type: mimeType
    }
  }

  /**
   * 根据文件扩展名获取 MIME 类型
   */
  private getMimeType(ext: string): string {
    const mimeTypes: Record<string, string> = {
      // 视频文件
      '.mp4': 'video/mp4',
      '.avi': 'video/avi',
      '.mov': 'video/quicktime',
      '.wmv': 'video/x-ms-wmv',
      '.flv': 'video/x-flv',
      '.webm': 'video/webm',
      '.mkv': 'video/x-matroska',

      // 图片文件
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.bmp': 'image/bmp',
      '.webp': 'image/webp',
      '.svg': 'image/svg+xml',

      // 音频文件
      '.mp3': 'audio/mpeg',
      '.wav': 'audio/wav',
      '.flac': 'audio/flac',
      '.aac': 'audio/aac',
      '.ogg': 'audio/ogg',
      '.m4a': 'audio/mp4'
    }

    return mimeTypes[ext] || 'application/octet-stream'
  }

  /**
   * 创建远程目录
   */
  private async createRemoteDirectory(
    folderName: string,
    parentId: string,
    resourceType: string
  ): Promise<string> {
    try {
      let url: string

      switch (resourceType) {
        case 'paster':
          url = '/app-api/creative/team/directory/paster/create'
          break
        case 'music':
          url = '/app-api/creative/team/directory/music/create'
          break
        case 'voice':
          url = '/app-api/creative/team/directory/voice/create'
          break
        case 'media':
        default:
          url = '/app-api/creative/storage/directory/create'
          break
      }

      const response = await this.requestService.post<string>(url, {
        folderName,
        parentId
      })

      this.logger.debug(`创建远程目录成功: ${folderName} -> ${response}`)
      return response
    } catch (error) {
      this.logger.error(`创建远程目录失败: ${folderName}`, error)
      throw new Error(`创建远程目录失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 批量创建远程目录结构
   */
  private async createRemoteDirectoryStructure(
    folderNode: FolderNode,
    parentFolderUuid: string,
    resourceType: string
  ): Promise<void> {
    // 为当前文件夹创建远程目录
    folderNode.folderUuid = await this.createRemoteDirectory(
      folderNode.name,
      parentFolderUuid,
      resourceType
    )

    // 递归为子文件夹创建远程目录
    for (const childFolder of folderNode.children) {
      await this.createRemoteDirectoryStructure(
        childFolder,
        folderNode.folderUuid!,
        resourceType
      )
    }
  }

  /**
   * 上传文件夹
   */
  async uploadFolder(data: FolderUploadParams): Promise<FolderUploadResult> {
    if (!data) {
      throw new IPCHandlerError('上传文件夹参数不能为空')
    }

    const {
      folderPath,
      parentFolderUuid,
      resourceType,
      uid,
      teamId,
      fileTypes = ['video/*', 'image/*', 'audio/mpeg'],
      maxSize = 100 * 1024 * 1024
    } = data

    if (!folderPath) {
      throw new IPCHandlerError('文件夹路径不能为空')
    }
    if (!parentFolderUuid) {
      throw new IPCHandlerError('父文件夹UUID不能为空')
    }
    if (!uid) {
      throw new IPCHandlerError('用户ID不能为空')
    }

    try {
      // 检查文件夹是否存在
      const stats = statSync(folderPath)
      if (!stats.isDirectory()) {
        return { success: false, error: '指定路径不是文件夹' }
      }

      this.logger.debug(`开始上传文件夹: ${folderPath}`)

      // 1. 遍历文件夹结构
      const folderStructure = await this.traverseFolder(folderPath, folderPath, fileTypes, maxSize)

      // 检查是否有有效文件
      const allFiles = this.collectAllFiles(folderStructure)
      if (allFiles.length === 0) {
        return { success: false, error: '文件夹中没有找到支持的文件类型' }
      }

      this.logger.debug(`找到 ${allFiles.length} 个有效文件`)

      // 2. 创建远程目录结构
      await this.createRemoteDirectoryStructure(folderStructure, parentFolderUuid, resourceType)

      // 3. 生成批次ID
      const batchId = `folder_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`

      // 4. 为每个文件创建上传任务
      const taskIds = await this.createUploadTasksForFiles(
        allFiles,
        folderStructure,
        batchId,
        uid,
        teamId,
        resourceType
      )

      this.logger.debug(`创建了 ${taskIds.length} 个上传任务`)

      return {
        success: true,
        batchId,
        taskIds,
        totalFiles: allFiles.length
      }
    } catch (error: any) {
      this.logger.error('上传文件夹失败:', error)
      return {
        success: false,
        error: error.message || '上传文件夹失败'
      }
    }
  }

  /**
   * 收集文件夹结构中的所有文件
   */
  private collectAllFiles(folderNode: FolderNode): Array<FileNode & { folderUuid: string }> {
    const files: Array<FileNode & { folderUuid: string }> = []

    // 添加当前文件夹的文件
    for (const file of folderNode.files) {
      files.push({
        ...file,
        folderUuid: folderNode.folderUuid!
      })
    }

    // 递归添加子文件夹的文件
    for (const childFolder of folderNode.children) {
      files.push(...this.collectAllFiles(childFolder))
    }

    return files
  }

  /**
   * 为文件列表创建上传任务
   */
  private async createUploadTasksForFiles(
    files: Array<FileNode & { folderUuid: string }>,
    folderStructure: FolderNode,
    batchId: string,
    uid: string,
    teamId: number | undefined,
    resourceType: string
  ): Promise<number[]> {
    const taskIds: number[] = []

    for (const file of files) {
      try {
        // 确定文件类型
        const fileType = this.getUploadTaskType(file.type)

        // 创建上传任务
        const task = await this.repository.create({
          uid,
          team_id: teamId || 0,
          name: file.name,
          local_path: file.path,
          type: fileType,
          folder_id: file.folderUuid,
          upload_module: resourceType
        })

        // 更新任务的文件大小信息
        await this.repository.update(task.id, {
          size: file.size
        })

        taskIds.push(task.id)

        this.logger.debug(`创建上传任务: ${file.name} -> 任务ID: ${task.id}`)
      } catch (error) {
        this.logger.error(`创建上传任务失败: ${file.name}`, error)
        // 继续处理其他文件，不中断整个流程
      }
    }

    return taskIds
  }

  /**
   * 根据 MIME 类型确定上传任务类型
   */
  private getUploadTaskType(mimeType: string): UploadTask.Type {
    if (mimeType.startsWith('video/')) {
      return UploadTask.Type.VIDEO
    } else if (mimeType.startsWith('image/')) {
      return UploadTask.Type.IMAGE
    } else if (mimeType.startsWith('audio/')) {
      return UploadTask.Type.AUDIO
    } else {
      return UploadTask.Type.OTHER
    }
  }
}
