import { UploadTask } from './types/database.types'

export const FILE_EXTENSIONS = {
  VIDEO: ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', 'm4v'] as const,
  AUDIO: ['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma', 'm4a'] as const,
  IMAGE: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'tiff'] as const,
  ALL: ['*'] as const,
} as const

export type FileCategory = keyof typeof FILE_EXTENSIONS
export type FileExtension = typeof FILE_EXTENSIONS[FileCategory][number]

/**
 * 按需生成 filters
 */
export function createFileFilters(categories: FileCategory[]) {
  const nameMap: Record<FileCategory, string> = {
    VIDEO: '视频文件',
    AUDIO: '音频文件',
    IMAGE: '图片文件',
    ALL: '所有文件'
  }

  return categories.map(category => ({
    name: nameMap[category],
    extensions: [...FILE_EXTENSIONS[category]],
  }))
}

export const getFileType = (fileName: string): UploadTask.Type => {
  const ext = fileName.toLowerCase().split('.').pop() as FileExtension | undefined
  if (!ext) return UploadTask.Type.OTHER
  
  // 遍历 FILE_EXTENSIONS 找匹配类型
  // @ts-ignore
  const category = (Object.keys(FILE_EXTENSIONS) as FileCategory[]).find(key => FILE_EXTENSIONS[key].includes(ext))

  switch (category) {
    case 'VIDEO': return UploadTask.Type.VIDEO
    case 'AUDIO': return UploadTask.Type.AUDIO
    case 'IMAGE': return UploadTask.Type.IMAGE
    default: return UploadTask.Type.OTHER
  }
}