import { ModalContent } from '@/components/modal'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Separator } from '@/components/ui/separator'
import { Skeleton } from '@/components/ui/skeleton'
import { useQueryCollaborators } from '@/hooks/queries/useQueryCollaborator'
import { useInfiniteQueryTeamMembers, useQueryTeamMemberByID } from '@/hooks/queries/useQueryTeam'
import { useIntersectionObserver } from '@/hooks/useIntersectionObserver'
import { useModal } from '@/libs/tools/modal'
import { Project } from '@/types/project'
import { BrushCleaning, Search } from 'lucide-react'
import React, { useMemo } from 'react'

function CollaboratorItem({ memberId }: { memberId: number }) {
  const { data: member } = useQueryTeamMemberByID(memberId)

  return member ? (
    <div className="flex items-center justify-start p-2 hover:bg-muted-foreground/5 rounded">
      <Avatar>
        <AvatarFallback style={{ backgroundImage: `linear-gradient(${member.id})` }}>
          {member.nickname.slice(0, 1).toUpperCase()}
        </AvatarFallback>
      </Avatar>
      <span className="ml-2">{member.nickname}</span>
      <Button variant="link" size="sm" className="ml-auto">
        添加
      </Button>
    </div>
  ) : (
    <div className="flex items-center justify-start p-2 hover:bg-muted-foreground/5 rounded">
      <Skeleton className="h-8 w-full" />
      <Skeleton className="h-6 w-3/4 mt-2" />
      <Skeleton className="h-6 w-1/2 mt-2" />
    </div>
  )
}

function ModalCollaborator({ project }: { project: Project }) {
  const [keyword, setKeyword] = React.useState('')
  const { data: membersData, isLoading: loadingMembers, fetchNextPage } = useInfiniteQueryTeamMembers()
  const { data: collaborators, isLoading: loadingCollaborators } = useQueryCollaborators(project.id)
  const scrollRef = React.useRef<HTMLDivElement>(null)
  const Detector = useIntersectionObserver({
    onIntersect: () => fetchNextPage(),
    deps: [membersData],
    root: scrollRef.current,
  })

  const members = useMemo(() => {
    const list = membersData?.pages.flatMap(page => page.list) || []
    return list.filter(member => !collaborators?.some(c => c.memberId === member.id))
  }, [membersData, collaborators])
  console.log(project)

  return (
    <ModalContent
      title={
        <div className="flex items-center justify-between pr-2">
          <div className="flex items-center gap-2">
            <span>添加协作者</span>
            <Badge variant="outline">{project.projectName}</Badge>
          </div>
          <div className="relative font-normal">
            <Input
              placeholder="请输入关键词搜索"
              value={keyword}
              onChange={e => setKeyword(e.target.value)}
              className="pr-10"
            />
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 size-4 text-muted-foreground" />
          </div>
        </div>
      }
    >
      <div className="flex flex-1 min-h-0">
        <div className="flex-1 min-h-0">
          <div className="h-6 flex items-center">
            <span>团队成员列表</span>
          </div>
          <Separator className="my-1" />
          {loadingMembers ? (
            <div className="flex items-center justify-center h-20">
              <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-blue-500" />
            </div>
          ) : (
            <div className="flex flex-col gap-2 overflow-auto h-full" ref={scrollRef}>
              {members
                ?.filter(member => member.nickname.includes(keyword))
                .flatMap(v =>
                  Array(20)
                    .fill(0)
                    .map((_, i) => ({ ...v, id: v.id * 100 + i })),
                ) // 模拟多条数据
                .map(member => (
                  <div
                    key={member.id}
                    className="flex items-center justify-start p-2 hover:bg-muted-foreground/5 rounded"
                  >
                    <Avatar>
                      <AvatarFallback style={{ backgroundImage: `linear-gradient(${member.id})` }}>
                        {member.nickname.slice(0, 1).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <span className="ml-2">{member.nickname}</span>
                    <Button variant="link" size="sm" className="ml-auto">
                      添加
                    </Button>
                  </div>
                ))}
              <Detector />
            </div>
          )}
        </div>
        <Separator orientation="vertical" className="mx-4" />
        <div className="flex-1">
          <div className="flex justify-between items-center h-6">
            <span>协作者列表</span>
            <Button variant="ghost" size="icon" className="size-6 rounded" title="清空协作者列表">
              <BrushCleaning className="size-4" />
            </Button>
          </div>
          <Separator className="my-1" />
          <div className="flex flex-col gap-2 overflow-auto h-full">
            {loadingCollaborators ? (
              <div className="flex items-center justify-center h-20">
                <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-blue-500" />
              </div>
            ) : (
              collaborators?.map(collaborator => (
                <CollaboratorItem key={collaborator.memberId} memberId={collaborator.memberId} />
              ))
            )}
          </div>
        </div>
      </div>
    </ModalContent>
  )
}

export function useProjectCollaboratorModal() {
  const modal = useModal()

  return (project: Project) => {
    modal({
      className: 'w-2xl h-120 max-w-full!',
      content: <ModalCollaborator project={project} />,
    })
  }
}
