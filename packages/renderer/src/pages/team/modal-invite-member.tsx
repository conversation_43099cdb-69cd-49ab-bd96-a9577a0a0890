import { ModalContent } from '@/components/modal'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { useQueryTeamRoles } from '@/hooks/queries/useQueryTeam'
import { useModal } from '@/libs/tools/modal'
import React from 'react'

function InviteMemberModal() {
  const { data } = useQueryTeamRoles()
  const roles = React.useMemo(() => data?.filter(role => role.code !== 'owner') || [], [data])
  const [selectedRole, setSelectedRole] = React.useState<string>('')

  return (
    <ModalContent title="邀请成员" description="邀请新成员加入团队">
      <div className="flex items-center justify-between">
        <span>权限角色</span>
        <Select value={selectedRole} onValueChange={setSelectedRole}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="选择角色" />
          </SelectTrigger>
          <SelectContent>
            {roles.map(role => (
              <SelectItem key={role.id} value={role.code}>
                {role.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      <Separator />
    </ModalContent>
  )
}

export function useModalInviteMember() {
  const modal = useModal()

  return () =>
    modal({
      content: <InviteMemberModal />,
    })
}
