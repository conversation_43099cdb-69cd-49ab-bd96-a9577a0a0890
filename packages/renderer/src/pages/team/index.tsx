import React, { useMemo } from 'react'
import { Skeleton } from '@/components/ui/skeleton'
import {
  useQueryCurrentTeam,
  useQueryTeamMemberByID,
  useQueryTeamMemberList,
  useQueryTeamRoles
} from '@/hooks/queries/useQueryTeam'
import { TeamAPI, TeamPermission } from '@/libs/request/api/team'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { Button } from '@/components/ui/button'
import { TeamManager, TokenManager } from '@/libs/storage'
import { toast } from 'react-toastify'
import { useDeleteModal } from '@/components/modal/delete'
import { useNavigate } from 'react-router'
import { useQueryClient } from '@tanstack/react-query'
import { CheckboxHeader, CheckboxItem, CheckboxProvider, useCheckboxContext } from '@/components/checkbox'
import { Table, TableBody, TableCaption, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { useModalInviteMember } from './modal-invite-member'

export default function TeamPageWrapper() {
  return (
    <CheckboxProvider>
      <TeamPage />
    </CheckboxProvider>
  )
}

function TeamPage() {
  const { data: team } = useQueryCurrentTeam()
  const { data: members } = useQueryTeamMemberList('')
  const { data: member } = useQueryTeamMemberByID(TokenManager.getUserId())
  const { data: roles, refetch } = useQueryTeamRoles()
  const queryClient = useQueryClient()
  const deleteModal = useDeleteModal()
  const modalInviteMember = useModalInviteMember()
  const navigate = useNavigate()
  const { selected, all } = useCheckboxContext()

  const editable = useMemo(() => {
    if (!member) return false
    return member.roles.some(role => {
      return ['owner', 'admin'].includes(role.code)
    })
  }, [member?.roles, roles])

  function SwitchItem({
    checked,
    field,
    memberId,
  }: {
    checked: boolean
    field: keyof TeamPermission
    memberId: number
  }) {
    return (
      <TableCell>
        <Switch
          disabled={!editable}
          checked={checked}
          onCheckedChange={async (checked: boolean) => {
            await TeamAPI.setMemberPermission({
              memberId,
              type: field,
              status: checked,
            })
            refetch()
          }}
        />
      </TableCell>
    )
  }

  return (
    <div className="flex flex-col items-stretch h-full px-4">
      <div className="p-4 w-full flex items-center justify-between">
        <div className="flex items-center gap-2">
          {team ? <h4 className="text-lg font-bold">{team.name}</h4> : <Skeleton className="h-8 w-48" />}
        </div>
        <div className="flex items-center gap-4">
          {members && (
            <div className="text-sm text-muted-foreground/70">
              已选择：{Object.keys(selected).length}/{Object.keys(all).length}
            </div>
          )}
          {member?.roles.some(role => ['owner', 'admin'].includes(role.code)) && (
            <Button variant="outline" onClick={modalInviteMember}>
              邀请成员
            </Button>
          )}
          {member?.roles.some(role => role.code === 'owner') && (
            <Button
              variant="destructive"
              onClick={() => {
                return deleteModal({
                  kind: '团队',
                  name: team?.name || '当前团队',
                  danger: true,
                  actionName: '解散',
                  action: async () => {
                    try {
                      await TeamAPI.disband({ teamId: TeamManager.current()! })
                      TeamManager.clear()
                      await queryClient.resetQueries()
                      navigate('/login/team')
                    } catch (error: any) {
                      toast.error(error?.message || '解散团队失败')
                    }
                  },
                })
              }}
            >
              解散团队
            </Button>
          )}
        </div>
      </div>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>
              <CheckboxHeader />
            </TableHead>
            <TableHead>姓名</TableHead>
            <TableHead>手机号</TableHead>
            <TableHead>角色</TableHead>
            {editable && (
              <>
                <TableHead>混剪权限</TableHead>
                <TableHead>发布权限</TableHead>
                <TableHead>抖音评论权限</TableHead>
                <TableHead>TikTok 评论权限</TableHead>
                <TableHead>私信管理权限</TableHead>
              </>
            )}
            <TableHead>操作</TableHead>
          </TableRow>
        </TableHeader>
        {members ? (
          <TableBody>
            {members.map(member => (
              <TableRow key={member.id}>
                <TableCell>
                  <CheckboxItem value={member.id} />
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <span className="text-sm">{member.nickname}</span>
                    {member.id === TokenManager.getUserId() && (
                      <Badge className="ml-2" variant="secondary">
                        我
                      </Badge>
                    )}
                  </div>
                </TableCell>
                <TableCell>{member.mobile}</TableCell>
                <TableCell>
                  {member.roles.map(role => (
                    <Badge key={role.id} className="mr-1" variant="outline">
                      {role.name}
                    </Badge>
                  ))}
                </TableCell>
                {editable && (
                  <>
                    <SwitchItem checked={member.isEditVideo} field="isEditVideo" memberId={member.id} />
                    <SwitchItem checked={member.isPublishVideo} field="isPublishVideo" memberId={member.id} />
                    <SwitchItem checked={member.isComment} field="isComment" memberId={member.id} />
                    <SwitchItem checked={member.isPrivateMsg} field="isPrivateMsg" memberId={member.id} />
                    <SwitchItem checked={member.isTiktokComment} field="isTiktokComment" memberId={member.id} />
                  </>
                )}
              </TableRow>
            ))}
          </TableBody>
        ) : (
          <TableCaption>
            <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-blue-500" />
          </TableCaption>
        )}
      </Table>
      {/* <DataTable<Member, unknown>
        columns={[
          {
            id: 'select',
            header: () => (
              <div>
                <CheckboxHeader />
              </div>
            ),
            cell: ({ row }) => (
              <div>
                <CheckboxItem value={row.original.memberId} />
              </div>
            ),
          },
          {
            accessorKey: 'nickname',
            header: '姓名',
            cell: ({ row }) => (
              <>
                <span className="text-sm">{row.original.nickname}</span>
                {row.original.memberId === TokenManager.getUserId() && (
                  <Badge className="ml-2" variant="secondary">
                    我
                  </Badge>
                )}
              </>
            ),
          },
          {
            accessorKey: 'mobile',
            header: '手机号',
            cell: ({ row }) => <span className="text-sm">{row.original.mobile}</span>,
          },
          switchColumn('isEditVideo', '混剪权限'),
          switchColumn('isPublishVideo', '发布权限'),
          switchColumn('isComment', '抖音评论权限'),
          switchColumn('isPrivateMsg', '私信管理权限'),
          switchColumn('isTiktokComment', 'TikTok 评论权限'),
          {
            header: '操作',
            cell: ({ row }) => {
              return (
                row.original.isCreator || (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <Ellipsis className="size-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        onClick={async () => {
                          return toast.info('此功能正在开发中，敬请期待！')
                          // TODO: 实现转移权限逻辑，此 API 文档不清晰，需要确认后端实现
                          await TeamAPI.transfer({})
                        }}
                      >
                        转移权限
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={async () => {
                          return toast.info('此功能正在开发中，敬请期待！')
                          // TODO: 实现删除用户逻辑，此 API 未提供
                        }}
                      >
                        删除用户
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                )
              )
            },
          },
        ]}
        data={members || []}
        pagination={paginationParams}
        // onPaginationChange={setPagination}
      /> */}
    </div>
  )
}
