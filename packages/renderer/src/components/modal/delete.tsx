import React, { useCallback, useState } from 'react'
import { CustomButton, ModalContent } from '../modal'
import { useModal, useModalContext } from '@/libs/tools/modal'

type Props = {
  kind: string
  name: string
  danger?: boolean
  action?: () => Promise<any> | any
  actionName?: string
  customButtons?: CustomButton[]
}

function DeleteModal({ kind, name, danger, action, actionName = '删除', customButtons }: Props) {
  const [pending, setPending] = useState(false)
  const { close } = useModalContext()

  const handleCustomButtonClick = async (buttonAction: () => Promise<any> | any) => {
    setPending(true)
    try {
      await buttonAction()
      close() // 自定义按钮操作完成后关闭模态框
    } finally {
      setPending(false)
    }
  }

  return (
    <ModalContent
      title={`${actionName}${kind}`}
      description={`确定要${actionName}${kind}「${name}」吗？${danger ? '此操作不可恢复！' : ''}`}
      pending={pending}
      onConfirm={async () => {
        setPending(true)
        try {
          await action?.()
          close()
        } finally {
          setPending(false)
        }
      }}
      customButtons={
        customButtons?.map(button => ({
          ...button,
          onClick: () => handleCustomButtonClick(button.onClick), // 自定义按钮绑定点击事件
        })) ?? undefined
      }
    />
  )
}

export function useDeleteModal() {
  const modal = useModal()
  return useCallback((props: Props) => modal({ content: <DeleteModal {...props} /> }), [])
}
