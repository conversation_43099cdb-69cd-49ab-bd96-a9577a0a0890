import { PaginationParams } from '@app/shared/types/database.types'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { ResourceModule } from '@/libs/request/api/resource'
import { useInfiniteQuery } from '../useInfiniteQuery'
import { useQuery } from '@tanstack/react-query'
import { safeParseJson } from '@app/shared/utils'
import { Script } from '@/types/project'

type ScriptQueryParams = PaginationParams & {
  projectId?: string
  keyword?: string
}

/**
 * 使用无限查询获取脚本列表，支持无限滚动加载
 * @param params 查询参数，包括分类ID和每页大小
 * @returns 无限查询结果
 */
export const useInfiniteQueryScriptList = (params: ScriptQueryParams = {}) => {
  return useInfiniteQuery([QUERY_KEYS.SCRIPT_LIST], ResourceModule.script.list, params, {
    pageSize: params.pageSize || 12,
  })
}

export interface ScriptSceneData {
  id: string
  /**
   * 分镜标题
   */
  title?: string
  shotType?: string
  script?: string
  notes?: string
  refImg?: string
}

/**
 * 使用查询获取脚本详情
 * @param id 脚本ID
 * @returns 查询结果
 */
export const useQueryScript = (id: string) => {
  return useQuery({
    queryKey: [QUERY_KEYS.SCRIPT_DETAIL, id],
    queryFn: async (): Promise<Script & { scenes: ScriptSceneData[] }> => {
      const data = await ResourceModule.script.get({ id })

      let parsed: ScriptSceneData[] = []
      if ('content' in data && typeof data.content === 'string') {
        parsed = safeParseJson<ScriptSceneData[]>(data.content) || []
      }

      return {
        ...data,
        scenes: parsed
      }
    },
  })
}
